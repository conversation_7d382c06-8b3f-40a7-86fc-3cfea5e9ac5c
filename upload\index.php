<?php
require_once '../includes/config.php';
$title = 'Upload Video - IslamTube';
include '../includes/header.php';
if (isset($_SESSION['userId'])) {
} else {
   header("Location: ../login");
}
?>
<body class="bg-gray-50">
    <?php
    $upload = true;
    ?>
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-sm   " id="upload-message">
            <!-- Enhanced Upload Form -->
            <div class="max-w-4xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
                <!-- Form Header -->
                <div class="bg-gradient-to-r from-green-600 to-blue-600 px-8 py-6">
                    <div class="flex items-center space-x-3">
                        <div class="bg-white bg-opacity-20 rounded-full p-3">
                            <i class="fas fa-cloud-upload-alt text-white text-2xl"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-white">Upload Islamic Video</h2>
                            <p class="text-green-100">Share beneficial knowledge with the Ummah</p>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="p-8">
                    <form class="space-y-8" hx-post="api/insertVideo.php" hx-target="#upload-message" hx-swap="innerHTML">
                        <!-- Video Details Section -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-video text-green-600 mr-2"></i>
                                Video Information
                            </h3>
                            
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Video URL -->
                                <div class="lg:col-span-2">
                                    <label for="videoUrl" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-link text-green-600 mr-1"></i>
                                        Video URL *
                                    </label>
                                    <div class="relative">
                                        <input type="url" id="videoUrl" name="videoUrl" required
                                            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                            placeholder="https://example.com/video.mp4">
                                        <i class="fas fa-globe absolute left-3 top-3.5 text-gray-400"></i>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">Enter the direct URL to your video file</p>
                                </div>

                                <!-- Thumbnail URL -->
                                <div class="lg:col-span-2">
                                    <label for="thumbnailUrl" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-image text-green-600 mr-1"></i>
                                        Thumbnail URL *
                                    </label>
                                    <div class="relative">
                                        <input type="url" id="thumbnailUrl" name="thumbnailUrl" required
                                            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                            placeholder="https://example.com/thumbnail.jpg">
                                        <i class="fas fa-camera absolute left-3 top-3.5 text-gray-400"></i>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">Recommended size: 1280x720 pixels (16:9 ratio)</p>
                                </div>
                                 <div class="lg:col-span-2">
                                    <label for="thumbnailUrl" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-clock text-green-600 mr-1"></i>
                                        Duration *
                                    </label>
                                    <div class="relative">
                                        <input type="text" id="duration" name="duration" required
                                            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                            placeholder="1:15:30">
                                        <i class="fas fa-clock absolute left-3 top-3.5 text-gray-400"></i>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">In this format 15:30.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Content Details Section -->
                        <div class="bg-blue-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-edit text-blue-600 mr-2"></i>
                                Content Details
                            </h3>
                            
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Title -->
                                <div class="lg:col-span-2">
                                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-heading text-blue-600 mr-1"></i>
                                        Video Title *
                                    </label>
                                    <input type="text" id="title" name="title" required
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                        placeholder="Enter an engaging title for your video"
                                        maxlength="100">
                                    <div class="flex justify-between mt-1">
                                        <p class="text-xs text-gray-500">Make it descriptive and engaging</p>
                                        <span class="text-xs text-gray-400" id="title-counter">0/100</span>
                                    </div>
                                </div>

                                <!-- Category -->
                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-tags text-blue-600 mr-1"></i>
                                        Category *
                                    </label>
                                    <div class="relative">
                                        <select id="category" name="category" required
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white">
                                            <option value="">Choose a category</option>
                                            <option value="1">📚 Ilm (Islamic Knowledge)</option>
                                            <option value="2">💪 Fitness & Health</option>
                                            <option value="3">🛠️ Skills & Education</option>
                                        </select>
                                        <i class="fas fa-chevron-down absolute right-3 top-3.5 text-gray-400 pointer-events-none"></i>
                                    </div>
                                </div>

                                <!-- Description -->
                                <div class="lg:col-span-2">
                                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-align-left text-blue-600 mr-1"></i>
                                        Description
                                    </label>
                                    <textarea id="description" name="description" rows="4"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-colors"
                                            placeholder="Describe your video content... What will viewers learn? Include relevant Islamic context if applicable."
                                            maxlength="500"></textarea>
                                    <div class="flex justify-between mt-1">
                                        <p class="text-xs text-gray-500">Provide context and key points covered in your video</p>
                                        <span class="text-xs text-gray-400" id="desc-counter">0/500</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Islamic Guidelines -->
                        <div class="bg-green-50 border-l-4 border-green-400 p-6 rounded-r-lg">
                            <div class="flex items-start">
                                <i class="fas fa-mosque text-green-600 text-xl mt-1 mr-3"></i>
                                <div>
                                    <h4 class="text-sm font-semibold text-green-800 mb-2">Islamic Content Guidelines</h4>
                                    <ul class="text-sm text-green-700 space-y-1">
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                            Content should be beneficial and align with Islamic teachings
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                            Verify authenticity of religious information shared
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                            Maintain proper Islamic etiquette and respect
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                            Avoid content that contradicts Islamic values
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row justify-between items-center pt-6 border-t border-gray-200 space-y-4 sm:space-y-0">
                            <div class="flex items-center space-x-2 text-sm text-gray-600">
                                <i class="fas fa-info-circle text-blue-500"></i>
                                <span>All fields marked with * are required</span>
                            </div>
                            
                            <div class="flex space-x-4">
                                <button type="button" class="hidden px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors font-medium">
                                    <i class="fas fa-save mr-2"></i>
                                    Save Draft
                                </button>
                                <button type="submit" class="bg-gradient-to-r from-green-600 to-green-700 text-white px-8 py-3 rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                    <i class="fas fa-cloud-upload-alt mr-2"></i>
                                    Upload Video
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Upload Output -->
                    <div id="upload-output" class="mt-6"></div>
                </div>
            </div>

        <!-- JvaScript for Character Counters -->
            <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Title character counter
                        const titleInput = document.getElementById('title');
                        const titleCounter = document.getElementById('title-counter');
                        
                        titleInput.addEventListener('input', function() {
                            const length = this.value.length;
                            titleCounter.textContent = `${length}/100`;
                            titleCounter.className = length > 90 ? 'text-xs text-red-500' : 'text-xs text-gray-400';
                        });
                        
                        // Description character counter
                        const descInput = document.getElementById('description');
                        const descCounter = document.getElementById('desc-counter');
                        
                        descInput.addEventListener('input', function() {
                            const length = this.value.length;
                            descCounter.textContent = `${length}/500`;
                            descCounter.className = length > 450 ? 'text-xs text-red-500' : 'text-xs text-gray-400';
                        });
                    });
            </script>
            <div id="upload-output"></div>
        </div>
    </main>
</body>
</html>
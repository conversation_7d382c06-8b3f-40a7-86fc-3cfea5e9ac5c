<?php

include '../../includes/config.php';
if (!isset($_SESSION['userId'])) {
    header("Location: ../../login");
}
$userId = $_SESSION['userId'];

if(isset($_POST['search'])){
    $search = '%' . $_POST['search'] . '%';
    $stmt = $pdo->prepare("SELECT * FROM video WHERE title LIKE :search AND uid = :userId ORDER BY id ASC LIMIT 10");
    $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':search', $search, PDO::PARAM_STR);
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_OBJ);
}else{  
  //using simple crud fetch videos from db and send in card like interface
 $videos = $db->video->select()  
        ->where('uid =', $userId)
        ->orderBy('id ASC')
        ->limit(10)
        ->get();
}
if (empty($videos) || $videos == "[]") {
    echo '<!-- No Videos Found Message -->
<div class="text-center py-16 px-4 lg:w-[400%] w-full">
    <div class="max-w-2xl mx-auto">
        <i class="fas fa-video-slash text-gray-300 text-6xl mb-6"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No Videos Found</h3>
        <p class="text-gray-600 mb-6">
            We couldnt find any videos matching your search. 
            <br>Try different search keyword
        </p>
       
    </div>
</div>';
    exit;
}
        foreach ($videos as $video) {
            // Format view count
            $viewCount = $video->views;
            
            if ($viewCount >= 1000000) {
                $formattedViews = number_format($viewCount/1000000, 1) . 'M';
            } else if ($viewCount >= 1000) {
                $formattedViews = number_format($viewCount/1000, 1) . 'K';
            } else {
                $formattedViews = $viewCount;
            }

            // Format upload time
            $uploadDate = $video->upload;
            // Convert string to DateTime object if needed (when using PDO search)
            if (is_string($uploadDate)) {
                $uploadDate = new DateTime($uploadDate);
            }
            $now = new DateTime();
            $interval = $now->diff($uploadDate);
            
            if ($interval->y > 0) {
                $timeAgo = $interval->y . ' year' . ($interval->y > 1 ? 's' : '') . ' ago';
            } elseif ($interval->m > 0) {
                $timeAgo = $interval->m . ' month' . ($interval->m > 1 ? 's' : '') . ' ago';
            } elseif ($interval->d > 6) {
                $weeks = floor($interval->d / 7);
                $timeAgo = $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
            } elseif ($interval->d > 0) {
                $timeAgo = $interval->d . ' day' . ($interval->d > 1 ? 's' : '') . ' ago';
            }else {
                $timeAgo = 'less than a day ago';
            }
            

?>
        <div class="p-6 hover:bg-gray-50">
                    <div class="flex items-center space-x-4">
                        <img src="<?php echo $video->thumbnailUrl;?>" alt="Video thumbnail" class="w-20 h-14 object-cover rounded">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1"><?php echo $video->title;?></h3>
                            <p class="text-sm text-gray-600 mb-2">A beautiful recitation of the opening chapter of the Holy Quran...</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span class="flex items-center">
                                    <i class="fas fa-eye mr-1"></i>
                                    <?php echo $formattedViews;?> views
                                </span>
                               
                               
                                <span>Published <?php echo $timeAgo;?></span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Published</span>
                            <div class="relative">
                                <button class="p-2 text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <!-- Dropdown menu would go here -->
                            </div>
                        </div>
                    </div>
                </div>
<?php
        }
 ?>
             
<?php
require_once '../includes/config.php';
$title = 'My Account - IslamTube';
include '../includes/header.php';
if (!isset($_SESSION['userId'])) {
    header("Location: ../login");
}
$userId = $_SESSION['userId'];
$user = $db->user->get(['id' => $userId]);
?>  

<body class="bg-gray-50">
     <?php include '../includes/navbar.php'; ?>
    

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-3 mb-2">
                <i class="fas fa-user-cog text-green-600 text-2xl"></i>
                <h1 class="text-3xl font-bold text-gray-900">My Account</h1>
            </div>
            <p class="text-gray-600">Assal<PERSON>u <PERSON>! Manage your profile and account settings</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm p-6 sticky top-8">
                    <nav class="space-y-2">
                        <a href="#profile" class="flex items-center space-x-3 px-4 py-3 text-green-600 bg-green-50 rounded-lg font-medium">
                            <i class="fas fa-user"></i>
                            <span>Profile Settings</span>
                        </a>
                        
                        <hr class="my-4">
                        <a href="<?php echo $mainUrl;?>myVideos" class="flex items-center space-x-3 px-4 py-3 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition">
                            <i class="fas fa-video"></i>
                            <span>My Videos</span>
                        </a>
                        <a href="<?php echo $mainUrl;?>channel?id=<?php echo $_SESSION['userId'];?>" class="flex items-center space-x-3 px-4 py-3 text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-lg transition">
                            <i class="fas fa-tv"></i>
                            <span>My Channel</span>
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="lg:col-span-3">
                <!-- Profile Settings Section -->
                <div id="profile" class="bg-white rounded-lg shadow-sm mb-8">
                    <!-- Section Header -->
                    <div class="border-b border-gray-200 px-8 py-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-xl font-semibold text-gray-900">Profile Information</h2>
                                <p class="text-gray-600 mt-1">Update your personal details and profile picture</p>
                            </div>
                            <div class="hidden text-sm text-gray-500">
                                <i class="fas fa-clock mr-1"></i>
                                Last updated: 2 days ago
                            </div>
                        </div>
                    </div>

                    <!-- Profile Form -->
                    <form class="p-8 space-y-8"  hx-encoding='multipart/form-data' hx-post="api/updateUser.php" hx-target="#profile-message" hx-swap="innerHTML">
                        <!-- Profile Picture Section -->
                        <div class="flex items-center space-x-6">
                            <div class="relative">
                                <img src="<?php echo $mainUrl;?><?php echo $user->avatar;?>" alt="Profile Picture" 
                                     class="w-24 h-24 rounded-full border-4 border-gray-200 object-cover" id="preview-image">
                                <label for="avatar-upload" class="absolute bottom-0 right-0 bg-green-600 text-white rounded-full p-2 hover:bg-green-700 transition shadow-lg cursor-pointer">
                                    <i class="fas fa-camera text-sm"></i>
                                </label>
                                <input type="file" id="avatar-upload" name="avatar" accept="image/*" class="hidden" 
                                       onchange="document.getElementById('preview-image').src = window.URL.createObjectURL(this.files[0])">
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Profile Picture</h3>
                                <p class="text-sm text-gray-600 mb-3">Upload a clear photo that represents you professionally</p>
                                <div class="flex space-x-3">
                                    <label for="avatar-upload" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition text-sm cursor-pointer">
                                        <i class="fas fa-upload mr-2"></i>Upload New
                                    </label>
                                   
                                </div>
                            </div>
                        </div>

                        <!-- Personal Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-user text-green-600 mr-1"></i>
                                    Account Name *
                                </label>
                                <input type="text" id="accountName" name="accountName" value="<?php echo $user->name;?>" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-envelope text-green-600 mr-1"></i>
                                    Email Address *
                                </label>
                                <input type="email" id="email" name="email" value="<?php echo $user->email;?>" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition">
                            </div>
                        </div>

                        

                        <div>
                            <label for="bio" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-align-left text-green-600 mr-1"></i>
                                Bio
                            </label>
                            <textarea id="bio" name="bio" rows="4" maxlength="500"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none transition"
                                      placeholder="Tell the Ummah about yourself and your Islamic journey..."><?php echo $user->description;?></textarea>
                            <div class="flex justify-between mt-1">
                                <p class="text-xs text-gray-500">Share your Islamic interests and background</p>
                                <span class="text-xs text-gray-400">156/500</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-1  gap-6">
                           
                            <div>
                                <label for="website" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-globe text-green-600 mr-1"></i>
                                    Contact
                                </label>
                                <input type="text" id="website" name="contact" value="<?php  echo $user->contact;?>"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition"
                                       placeholder="<EMAIL>">
                            </div>
                        </div>

                        

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row justify-between items-center pt-8 border-t space-y-4 sm:space-y-0">
                            <div class="flex items-center space-x-2 text-md text-gray-600">
                                <span id="profile-message"></span>
                            </div>
                            <div class="flex space-x-4">
                                
                                <button type="submit" class="bg-gradient-to-r from-green-600 to-green-700 text-white px-8 py-3 rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl">
                                    <i class="fas fa-save mr-2"></i>
                                    Save Changes
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Quick Actions Card -->
                <div class="hidden bg-gradient-to-r from-green-600 to-blue-600 rounded-lg p-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold mb-2">Ready to Share More Knowledge?</h3>
                            <p class="text-green-100 mb-4">Continue spreading beneficial Islamic content with the Ummah</p>
                            <div class="flex space-x-4">
                                <a href="upload.html" class="bg-white text-green-600 px-6 py-3 rounded-lg hover:bg-gray-100 transition font-medium">
                                    <i class="fas fa-plus mr-2"></i>
                                    Upload Video
                                </a>
                                <a href="my-videos.html" class="border border-white text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition font-medium">
                                    <i class="fas fa-video mr-2"></i>
                                    Manage Videos
                                </a>
                            </div>
                        </div>
                        <div class="hidden md:block">
                            <i class="fas fa-mosque text-6xl text-white opacity-20"></i>
                        </div>
                    </div>
                </div>

                <!-- Account Stats -->
                <div class="hidden grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                    <div class="bg-white rounded-lg shadow-sm p-6 text-center">
                        <div class="bg-blue-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-video text-blue-600 text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">12</h3>
                        <p class="text-gray-600">Videos Uploaded</p>
                    </div>
                    <div class="bg-white rounded-lg shadow-sm p-6 text-center">
                        <div class="bg-green-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-users text-green-600 text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">1.2K</h3>
                        <p class="text-gray-600">Subscribers</p>
                    </div>
                    <div class="bg-white rounded-lg shadow-sm p-6 text-center">
                        <div class="bg-purple-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-eye text-purple-600 text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">45K</h3>
                        <p class="text-gray-600">Total Views</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 IslamTube. Built with love for the Muslim Ummah. May Allah bless this platform.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript for form interactions -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Bio character counter
        const bioTextarea = document.getElementById('bio');
        const bioCounter = bioTextarea.parentElement.querySelector('.text-xs.text-gray-400');
        
        bioTextarea.addEventListener('input', function() {
            const length = this.value.length;
            bioCounter.textContent = `${length}/500`;
            bioCounter.className = length > 450 ? 'text-xs text-red-500' : 'text-xs text-gray-400';
        });

        // Smooth scrolling for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    });
    </script>
</body>
</html>
<?php
require_once 'includes/config.php';
$title = 'IslamTube - Islamic Video Platform';
include 'includes/header.php';
?>

<body class="bg-gray-50">
   <?php include 'includes/navbar.php'; ?>

    <!-- Navigation -->
    <nav class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex space-x-8 py-3">
                <a href="#" hx-post="mainApis/fetchVideos.php" hx-target="#videosArea" hx-swap="outerHTML" hx-vals='{"category":0}' class="text-green-600 border-b-2 border-green-600 pb-3 font-medium" onclick="setActiveLink(this)">All</a>
                <a href="#" hx-post="mainApis/fetchVideos.php" hx-target="#videosArea" hx-swap="outerHTML" hx-vals='{"category":1}' class="text-gray-600 hover:text-gray-900 pb-3" onclick="setActiveLink(this)">Knowledge</a>
                <a href="#" hx-post="mainApis/fetchVideos.php" hx-target="#videosArea" hx-swap="outerHTML" hx-vals='{"category":2}' class="text-gray-600 hover:text-gray-900 pb-3" onclick="setActiveLink(this)">Fitness</a>
                <a href="#" hx-post="mainApis/fetchVideos.php" hx-target="#videosArea" hx-swap="outerHTML" hx-vals='{"category":3}' class="text-gray-600 hover:text-gray-900 pb-3" onclick="setActiveLink(this)">Skills</a>
            </div>
            <script>
                function setActiveLink(element) {
                    // Remove active classes from all links
                    document.querySelectorAll('.flex.space-x-8.py-3 a').forEach(link => {
                        link.classList.remove('text-green-600', 'border-b-2', 'border-green-600', 'font-medium');
                    });
                    // Add active classes to clicked link
                    element.classList.add('text-green-600', 'border-b-2', 'border-green-600', 'font-medium');
                }
            </script>
        </div>
    </nav>

    <!-- Main Content -->
    <main  hx-post="mainApis/fetchVideos.php" id="mainContent"  hx-target="this" hx-trigger="load" hx-swap="innerHTML"  class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
       
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 IslamTube. Built with love for the Muslim Ummah. May Allah bless this platform.</p>
            </div>
        </div>
    </footer>
</body>
</html>